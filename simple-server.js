// 简单的测试服务器
const express = require('express')
const path = require('path')

console.log('启动简单服务器...')

const app = express()
app.use(express.static(path.join(__dirname, 'public')))

const server = app.listen(3001, () => {
    console.log('HTTP服务器已启动在端口 3001')
    console.log('访问: http://localhost:3001')
})

server.on('error', (err) => {
    console.error('HTTP服务器错误:', err)
})

// 测试WebSocket
try {
    const { WebSocketServer } = require('ws')
    const wss = new WebSocketServer({ port: 8081 })
    
    wss.on('connection', (ws) => {
        console.log('WebSocket客户端已连接')
        ws.send(JSON.stringify({ type: 'welcome', message: '欢迎连接' }))
    })
    
    wss.on('error', (err) => {
        console.error('WebSocket错误:', err)
    })
    
    console.log('WebSocket服务器已启动在端口 8081')
} catch (error) {
    console.error('WebSocket启动失败:', error)
}
