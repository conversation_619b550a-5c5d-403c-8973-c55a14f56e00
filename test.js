// 测试文件 - 检查模块导入是否正常
console.log('开始测试...')

try {
    console.log('测试 WebSocket 模块...')
    const { WebSocketServer } = require('ws')
    console.log('✓ WebSocket 模块正常')

    console.log('测试 Express 模块...')
    const express = require('express')
    console.log('✓ Express 模块正常')

    console.log('测试 Path 模块...')
    const path = require('path')
    console.log('✓ Path 模块正常')

    console.log('测试业务模块...')
    const { startMainProcess } = require('./src/fuck/main.js')
    console.log('✓ startMainProcess 模块正常')

    const { startScheduleProcess } = require('./src/row/main.js')
    console.log('✓ startScheduleProcess 模块正常')

    console.log('所有模块测试通过！')
    
} catch (error) {
    console.error('模块测试失败:', error.message)
    console.error('错误堆栈:', error.stack)
}
