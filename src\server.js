/**
 * 主服务器文件 - 提供WebSocket和HTTP服务
 * 
 * 功能说明：
 * 1. 启动Express静态文件服务器，提供前端页面访问
 * 2. 启动WebSocket服务器，处理客户端连接和消息通信
 * 3. 管理客户端连接状态，防止重复运行
 * 4. 提供日志记录功能，支持不同类型的消息输出
 * 5. 处理三种类型的启动请求：fuckStart、rowStart、scheduleStart
 * 6. 自动打开浏览器访问应用页面
 * 
 * 端口配置：
 * - HTTP服务器：3000端口
 * - WebSocket服务器：8080端口
 * 
 * 依赖模块：
 * - ws: WebSocket服务器
 * - express: HTTP服务器
 * - path: 路径处理
 * - open: 自动打开浏览器
 */

const { WebSocketServer } = require('ws')
const express = require('express')
const path = require('path')

// 导入业务模块
const { startMainProcess } = require('./fuck/main.js')
const { startScheduleProcess } = require('./row/main.js')

// 创建Express应用实例
const app = express()
// 设置静态文件目录，提供前端页面访问
app.use(express.static(path.join(__dirname, '../public')))
// 启动HTTP服务器，监听3001端口
app.listen(3001, () => {
  console.log('HTTP服务器已启动在端口 3001')
})

// 创建WebSocket服务器，监听8081端口
const wss = new WebSocketServer({ port: 8081 })
// 存储客户端连接信息，key为客户端ID，value为连接对象和状态
const clients = new Map()

// WebSocket连接事件处理
wss.on('connection', (ws) => {
  // 生成唯一的客户端ID：时间戳 + 随机字符串
  const clientId = Date.now() + Math.random().toString(36).substr(2, 9)
  // 将新客户端添加到连接池中，初始状态为idle
  clients.set(clientId, {
    ws,
    status: 'idle',
  })

  // 创建日志记录器对象，提供不同类型的消息输出功能
  const logger = {
    // 普通日志消息
    SJYssr: (...args) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'log', data: args.join(' ') }))
      }
    },
    // 错误日志消息
    SJYssrerror: (...args) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'error', data: args.join(' ') }))
      }
    },
    // 成功日志消息
    SJYssrgood: (...args) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'good', data: args.join(' ') }))
      }
    },
    // 表格数据消息
    SJYssrtable: (obj) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'table', data: obj }))
      }
    },
    // 进度步骤消息
    SJYssrstep: (num) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'step', data: num }))
      }
    },
    // Cookie数据消息
    cookie: (cookie) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'cookie', data: cookie }))
      }
    },
    // Profile ID数据消息
    profileId: (profileId) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'profileId', data: profileId }))
      }
    },
    // 课程数据消息
    lessonDatas: (lessonDatas) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'lessonDatas', data: lessonDatas }))
      }
    },
  }

  // 处理客户端发送的消息
  ws.on('message', async (data) => {
    try {
      // 解析客户端发送的JSON消息
      const message = JSON.parse(data)
      logger.SJYssrgood('收到启动请求')
      
      // 检查客户端状态，防止重复运行
      if (clients.get(clientId).status === 'running') {
        logger.SJYssrgood('已经有运行的实例了, 请稍后再试')
        return
      }
      
      // 根据消息类型执行不同的业务逻辑
      if (message.type === 'fuckStart') {
        // 自动选课模式
        clients.get(clientId).status = 'running'
        ws.send(JSON.stringify({ type: 'fuckStarted' }))
        message.config.logger = logger
        await startMainProcess(message.config)
      } else if (message.type === 'rowStart') {
        // 排课模式
        clients.get(clientId).status = 'running'
        ws.send(JSON.stringify({ type: 'rowStarted' }))
        message.config.logger = logger
        await startScheduleProcess(message.config)
      } else if (message.type === 'scheduleStart') {
        // 定时排课模式
        clients.get(clientId).status = 'running'
        ws.send(JSON.stringify({ type: 'scheduleStarted' }))
        message.config.logger = logger
        await startScheduleProcess(message.config)
      }
    } catch (error) {
      // 处理消息解析或业务执行错误
      logger.SJYssrerror('消息处理错误:', error)
      console.log(error);
    } finally {
      // 无论成功失败，都要重置客户端状态并发送结束消息
      clients.get(clientId).status = 'idle'
      ws.send(JSON.stringify({ type: 'fuckEnded' }))
      ws.send(JSON.stringify({ type: 'scheduleEnded' }))
    }
  })

  // 处理客户端连接关闭
  ws.on('close', () => {
    clients.delete(clientId)
  })

  // 处理WebSocket错误
  ws.on('error', (error) => {
    clients.delete(clientId)
  })
})

// WebSocket服务器错误处理
wss.on('error', (err) => {
  console.error('WebSocket 错误详情:', err)
  if (err.code === 'EADDRINUSE') {
    // 端口被占用错误
    console.clear();
    console.error(`端口 8081 已被占用，已有实例在运行。`)
    process.exit(1)
  } else {
    // 其他WebSocket错误
    console.error('WebSocket 启动错误:', err)
    process.exit(1)
  }
})

// 启动信息显示和浏览器自动打开
async function updateTable() {
  try {
    console.clear();
    console.log('=== 智能选课系统启动中 ===')
    console.log(`WebSocket端口: 8081`)
    console.log(`静态服务器: http://localhost:3001`)
    console.log(`浏览器打开上面的链接`)
    console.log(`不要关闭当前窗口`)
    console.log('==============================')

    // 动态导入open模块并自动打开浏览器
    const { default: open } = await import('open');
    await open(`http://localhost:3001`);
    console.log('浏览器已自动打开')
  } catch (error) {
    console.error('启动过程中出现错误:', error.message)
    console.error('错误详情:', error)
  }
}

// 添加全局错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason)
  process.exit(1)
})

// 执行启动流程
console.log('开始启动服务器...')
updateTable();

